#!/usr/bin/env python3
"""
快速代理测试和爬虫启动工具
验证代理配置并启动优化后的爬虫
"""

import asyncio
import aiohttp
import time
import os
from typing import Tuple, List
from config import PROXY_CONFIG

class QuickProxyTest:
    """快速代理测试"""
    
    def __init__(self):
        # 使用自动检测到的本地代理
        self.proxy_url = "http://127.0.0.1:7890"
        
    async def test_proxy_connection(self) -> Tuple[bool, str]:
        """测试代理连接"""
        try:
            print(f"🔍 测试代理连接: {self.proxy_url}")
            
            timeout = aiohttp.ClientTimeout(total=10)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                start_time = time.time()
                
                async with session.get(
                    "https://httpbin.org/ip",
                    proxy=self.proxy_url
                ) as response:
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        ip_data = await response.json()
                        origin_ip = ip_data.get('origin', 'unknown')
                        
                        print(f"✅ 代理连接成功!")
                        print(f"   响应时间: {response_time:.2f}秒")
                        print(f"   出口IP: {origin_ip}")
                        
                        return True, f"连接成功，出口IP: {origin_ip}"
                    else:
                        return False, f"HTTP状态码: {response.status}"
                        
        except Exception as e:
            print(f"❌ 代理连接失败: {str(e)}")
            return False, str(e)
    
    async def test_boss_access(self) -> Tuple[bool, str, str]:
        """测试BOSS直聘访问"""
        try:
            print(f"🎯 测试BOSS直聘访问...")
            
            timeout = aiohttp.ClientTimeout(total=15)
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            }
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # 先访问首页
                async with session.get(
                    "https://www.zhipin.com",
                    proxy=self.proxy_url,
                    headers=headers
                ) as response:
                    
                    if response.status != 200:
                        return False, f"首页访问失败: HTTP {response.status}", ""
                
                # 等待一下
                await asyncio.sleep(2)
                
                # 访问职位页面
                async with session.get(
                    "https://www.zhipin.com/web/geek/jobs?city=101010100&position=100000&page=1",
                    proxy=self.proxy_url,
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        html = await response.text()
                        page_length = len(html)
                        
                        # 分析页面类型
                        page_type = self._analyze_page_type(html)
                        
                        print(f"✅ BOSS访问成功!")
                        print(f"   页面长度: {page_length}")
                        print(f"   页面类型: {page_type}")
                        
                        return True, page_type, html
                    else:
                        return False, f"职位页面访问失败: HTTP {response.status}", ""
                        
        except Exception as e:
            print(f"❌ BOSS访问失败: {str(e)}")
            return False, str(e), ""
    
    def _analyze_page_type(self, html_content: str) -> str:
        """分析页面类型"""
        if "网站访客身份验证" in html_content or "verify-slider" in html_content:
            return "滑块验证页面"
        elif "请稍候" in html_content or "正在加载中" in html_content:
            return "安全检查页面"
        elif "job-list" in html_content or "职位" in html_content:
            return "职位列表页面"
        elif len(html_content) < 5000:
            return "错误页面"
        else:
            return "未知页面类型"

async def run_optimized_crawler():
    """运行优化后的爬虫"""
    print("\n🚀 启动优化后的爬虫...")
    print("="*50)
    
    try:
        # 导入优化后的爬虫
        from final_optimized_crawler import FinalOptimizedCrawler
        
        crawler = FinalOptimizedCrawler()
        
        # 测试爬取北京的职位（限制页数）
        city_code = "101010100"
        city_name = "北京"
        max_pages = 2  # 限制页数进行测试
        
        print(f"📍 目标城市: {city_name}")
        print(f"📄 最大页数: {max_pages}")
        print("-"*50)
        
        urls = await crawler.crawl_city_positions(city_code, city_name, max_pages)
        
        crawler.print_statistics()
        
        if urls:
            print(f"\n✅ 爬取完成，共发现 {len(urls)} 个职位URL")
            
            # 显示前几个URL作为示例
            if len(urls) > 0:
                print("\n📋 发现的URL示例:")
                for i, url in enumerate(urls[:5], 1):
                    print(f"  {i}. {url}")
                if len(urls) > 5:
                    print(f"  ... 还有 {len(urls) - 5} 个URL")
            
            print("\n📁 所有URL已保存到 output/ 目录")
            return True
        else:
            print("\n⚠️ 未发现任何URL")
            return False
            
    except ImportError as e:
        print(f"❌ 导入爬虫模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 爬虫运行异常: {str(e)}")
        return False

async def main():
    """主函数"""
    print("🔧 代理配置验证和爬虫启动工具")
    print("="*60)
    print(f"📍 本地代理: 127.0.0.1:7890 (ClashX Pro)")
    print(f"📍 出口IP: ***************")
    print(f"✅ 已自动配置到 config.py")
    print("="*60)
    
    tester = QuickProxyTest()
    
    # 步骤1: 测试代理连接
    print("\n📡 步骤1: 测试代理连接")
    print("-"*30)
    
    proxy_ok, proxy_msg = await tester.test_proxy_connection()
    
    if not proxy_ok:
        print(f"\n❌ 代理连接失败: {proxy_msg}")
        print("\n💡 可能的解决方案:")
        print("  1. 检查代理服务是否启动")
        print("  2. 确认端口7890是否正确")
        print("  3. 检查防火墙设置")
        print("  4. 尝试其他端口 (8080, 3128, 1080)")
        return
    
    # 步骤2: 测试BOSS直聘访问
    print("\n🎯 步骤2: 测试BOSS直聘访问")
    print("-"*30)
    
    boss_ok, page_type, html = await tester.test_boss_access()
    
    if not boss_ok:
        print(f"\n❌ BOSS访问失败: {page_type}")
        return
    
    # 保存测试页面
    os.makedirs("debug", exist_ok=True)
    with open("debug/proxy_test_page.html", "w", encoding="utf-8") as f:
        f.write(html)
    print(f"📄 测试页面已保存: debug/proxy_test_page.html")
    
    # 步骤3: 根据页面类型决定下一步
    print(f"\n📊 步骤3: 分析结果")
    print("-"*30)
    
    if page_type == "职位列表页面":
        print("✅ 完美！直接获取到职位页面，无需额外处理")
        should_run_crawler = True
    elif page_type == "滑块验证页面":
        print("⚠️ 遇到滑块验证，爬虫将尝试自动处理")
        should_run_crawler = True
    elif page_type == "安全检查页面":
        print("⚠️ 遇到安全检查，爬虫将尝试智能绕过")
        should_run_crawler = True
    else:
        print(f"❓ 未知页面类型: {page_type}")
        print("🔍 建议检查 debug/proxy_test_page.html 分析页面内容")
        should_run_crawler = False
    
    # 步骤4: 运行爬虫
    if should_run_crawler:
        print(f"\n🚀 步骤4: 运行优化后的爬虫")
        print("-"*30)
        
        crawler_success = await run_optimized_crawler()
        
        if crawler_success:
            print("\n🎉 爬虫运行成功！")
        else:
            print("\n⚠️ 爬虫运行遇到问题，但代理配置正常")
    else:
        print(f"\n⏸️ 暂停爬虫运行，需要进一步分析页面")
    
    # 总结
    print("\n" + "="*60)
    print("📋 测试总结")
    print("="*60)
    print(f"代理连接: {'✅ 成功' if proxy_ok else '❌ 失败'}")
    print(f"BOSS访问: {'✅ 成功' if boss_ok else '❌ 失败'}")
    print(f"页面类型: {page_type}")
    
    if proxy_ok and boss_ok:
        print(f"\n💡 下一步建议:")
        if page_type == "职位列表页面":
            print("  🚀 可以直接运行完整爬虫: python final_optimized_crawler.py")
        else:
            print("  🧩 运行滑块处理版本: python slider_captcha_solver.py")
            print("  🔧 或调整爬虫参数以适应当前页面类型")

if __name__ == "__main__":
    asyncio.run(main())
