#!/usr/bin/env python3
"""
自动代理检测和配置工具
自动检测本地活跃的代理服务并配置到爬虫系统
"""

import subprocess
import re
import asyncio
import aiohttp
import time
from typing import List, Dict, Tuple

class AutoProxyDetector:
    """自动代理检测器"""
    
    def __init__(self):
        self.common_proxy_ports = [7890, 7891, 1080, 1087, 8080, 8888, 3128, 9050, 9051]
        self.detected_proxies = []
        
    def detect_listening_ports(self) -> List[int]:
        """检测本地监听的代理端口"""
        try:
            print("🔍 检测本地监听端口...")
            
            # 使用netstat检测监听端口
            result = subprocess.run(
                ["netstat", "-an"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if result.returncode != 0:
                print("❌ netstat命令执行失败")
                return []
            
            listening_ports = []
            
            # 解析netstat输出
            for line in result.stdout.split('\n'):
                if 'LISTEN' in line and '127.0.0.1' in line:
                    # 匹配端口号
                    match = re.search(r'127\.0\.0\.1\.(\d+)', line)
                    if match:
                        port = int(match.group(1))
                        if port in self.common_proxy_ports:
                            listening_ports.append(port)
            
            # 去重并排序
            listening_ports = sorted(list(set(listening_ports)))
            
            print(f"✅ 检测到代理端口: {listening_ports}")
            return listening_ports
            
        except Exception as e:
            print(f"❌ 端口检测失败: {str(e)}")
            return []
    
    def detect_proxy_processes(self) -> List[str]:
        """检测代理相关进程"""
        try:
            print("🔍 检测代理进程...")
            
            result = subprocess.run(
                ["ps", "aux"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if result.returncode != 0:
                return []
            
            proxy_processes = []
            proxy_keywords = ['clash', 'v2ray', 'shadowsocks', 'proxy', 'surge', 'quantumult']
            
            for line in result.stdout.split('\n'):
                line_lower = line.lower()
                for keyword in proxy_keywords:
                    if keyword in line_lower and 'grep' not in line_lower:
                        proxy_processes.append(line.strip())
                        break
            
            if proxy_processes:
                print(f"✅ 检测到代理进程:")
                for process in proxy_processes:
                    # 只显示进程名部分
                    parts = process.split()
                    if len(parts) > 10:
                        process_name = ' '.join(parts[10:12])
                        print(f"   - {process_name}")
            else:
                print("⚠️ 未检测到明显的代理进程")
            
            return proxy_processes
            
        except Exception as e:
            print(f"❌ 进程检测失败: {str(e)}")
            return []
    
    async def test_proxy_endpoint(self, host: str, port: int, protocol: str = "http") -> Dict:
        """测试代理端点"""
        proxy_url = f"{protocol}://{host}:{port}"
        
        result = {
            "proxy_url": proxy_url,
            "host": host,
            "port": port,
            "protocol": protocol,
            "working": False,
            "response_time": 0,
            "error": None,
            "ip_info": None
        }
        
        try:
            timeout = aiohttp.ClientTimeout(total=5)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                start_time = time.time()
                
                async with session.get(
                    "https://httpbin.org/ip",
                    proxy=proxy_url
                ) as response:
                    response_time = time.time() - start_time
                    result["response_time"] = response_time
                    
                    if response.status == 200:
                        ip_data = await response.json()
                        result["working"] = True
                        result["ip_info"] = ip_data
                        
                        origin_ip = ip_data.get('origin', 'unknown')
                        print(f"✅ {proxy_url} - 工作正常 ({response_time:.2f}s) - 出口IP: {origin_ip}")
                    else:
                        result["error"] = f"HTTP {response.status}"
                        print(f"❌ {proxy_url} - HTTP {response.status}")
                        
        except Exception as e:
            result["error"] = str(e)
            print(f"❌ {proxy_url} - {str(e)}")
        
        return result
    
    async def comprehensive_detection(self) -> List[Dict]:
        """综合检测"""
        print("🚀 开始综合代理检测")
        print("="*50)
        
        # 步骤1: 检测监听端口
        listening_ports = self.detect_listening_ports()
        
        # 步骤2: 检测代理进程
        proxy_processes = self.detect_proxy_processes()
        
        # 步骤3: 测试代理端点
        print(f"\n🧪 测试代理端点...")
        print("-"*30)
        
        test_tasks = []
        
        # 测试检测到的端口
        for port in listening_ports:
            # HTTP代理
            test_tasks.append(self.test_proxy_endpoint("127.0.0.1", port, "http"))
            # SOCKS5代理
            test_tasks.append(self.test_proxy_endpoint("127.0.0.1", port, "socks5"))
        
        # 如果没有检测到端口，测试常见端口
        if not listening_ports:
            print("⚠️ 未检测到监听端口，测试常见代理端口...")
            for port in [7890, 1080, 8080]:
                test_tasks.append(self.test_proxy_endpoint("127.0.0.1", port, "http"))
                test_tasks.append(self.test_proxy_endpoint("127.0.0.1", port, "socks5"))
        
        # 并发测试
        results = await asyncio.gather(*test_tasks, return_exceptions=True)
        
        # 过滤有效结果
        working_proxies = []
        for result in results:
            if isinstance(result, dict) and result.get("working", False):
                working_proxies.append(result)
        
        return working_proxies
    
    def generate_config(self, working_proxies: List[Dict]) -> str:
        """生成配置代码"""
        if not working_proxies:
            return """
# 未检测到可用代理，请手动配置
PROXY_CONFIG = {
    "enable_proxy": False,
    "proxy_pool": [
        # "http://127.0.0.1:7890",
        # "socks5://127.0.0.1:7890",
    ]
}
"""
        
        proxy_urls = [proxy["proxy_url"] for proxy in working_proxies]
        
        config_lines = [
            "# 自动检测到的代理配置",
            "PROXY_CONFIG = {",
            '    "enable_proxy": True,',
            '    "proxy_pool": ['
        ]
        
        for proxy_url in proxy_urls:
            config_lines.append(f'        "{proxy_url}",')
        
        config_lines.extend([
            "    ]",
            "}"
        ])
        
        return "\n".join(config_lines)
    
    def update_config_file(self, working_proxies: List[Dict]):
        """更新配置文件"""
        if not working_proxies:
            print("⚠️ 未检测到可用代理，保持现有配置")
            return
        
        try:
            # 读取当前配置文件
            with open("config.py", "r", encoding="utf-8") as f:
                content = f.read()
            
            # 生成新的代理列表
            proxy_urls = [proxy["proxy_url"] for proxy in working_proxies]
            
            # 构建新的proxy_pool配置
            new_proxy_pool = '    "proxy_pool": [\n'
            new_proxy_pool += '        # 自动检测到的本地代理\n'
            
            for proxy_url in proxy_urls:
                new_proxy_pool += f'        "{proxy_url}",\n'
            
            new_proxy_pool += '        \n'
            new_proxy_pool += '        # 备用代理配置\n'
            new_proxy_pool += '        # "http://username:<EMAIL>:8080",\n'
            new_proxy_pool += '    ],'
            
            # 使用正则表达式替换proxy_pool部分
            pattern = r'"proxy_pool":\s*\[.*?\],'
            new_content = re.sub(pattern, new_proxy_pool, content, flags=re.DOTALL)
            
            # 写回文件
            with open("config.py", "w", encoding="utf-8") as f:
                f.write(new_content)
            
            print(f"✅ 已更新config.py，配置了{len(proxy_urls)}个代理")
            
        except Exception as e:
            print(f"❌ 更新配置文件失败: {str(e)}")

async def main():
    """主函数"""
    print("🔧 自动代理检测和配置工具")
    print("="*50)
    
    detector = AutoProxyDetector()
    
    # 执行综合检测
    working_proxies = await detector.comprehensive_detection()
    
    # 显示结果
    print(f"\n📊 检测结果")
    print("="*50)
    
    if working_proxies:
        print(f"✅ 检测到 {len(working_proxies)} 个可用代理:")
        
        for i, proxy in enumerate(working_proxies, 1):
            proxy_url = proxy["proxy_url"]
            response_time = proxy["response_time"]
            origin_ip = proxy["ip_info"].get("origin", "unknown") if proxy["ip_info"] else "unknown"
            
            print(f"  {i}. {proxy_url}")
            print(f"     响应时间: {response_time:.2f}s")
            print(f"     出口IP: {origin_ip}")
        
        # 更新配置文件
        print(f"\n🔧 更新配置文件...")
        detector.update_config_file(working_proxies)
        
        # 显示配置代码
        print(f"\n📋 生成的配置:")
        print("-"*30)
        config_code = detector.generate_config(working_proxies)
        print(config_code)
        
        print(f"\n💡 下一步建议:")
        print("  🚀 运行爬虫测试: python quick_test_with_proxy.py")
        print("  🧩 或运行完整爬虫: python final_optimized_crawler.py")
        
    else:
        print("❌ 未检测到可用代理")
        print("\n💡 可能的原因:")
        print("  1. 代理服务未启动")
        print("  2. 代理端口不在常见范围内")
        print("  3. 代理需要认证")
        print("  4. 防火墙阻止连接")
        
        print(f"\n🔧 手动配置建议:")
        print("  1. 启动ClashX/V2Ray等代理软件")
        print("  2. 检查代理软件的HTTP端口设置")
        print("  3. 确保允许局域网连接")

if __name__ == "__main__":
    asyncio.run(main())
