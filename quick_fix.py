#!/usr/bin/env python3
"""
快速修复脚本
解决当前发现的技术问题
"""

import asyncio
import sys
import os

async def test_basic_functionality():
    """测试基本功能"""
    print("🔧 开始快速修复和测试...")
    
    try:
        # 测试导入
        print("1. 测试模块导入...")
        from advanced_proxy_manager import proxy_manager
        from intelligent_anti_crawler import anti_crawler
        print("✅ 模块导入成功")
        
        # 测试代理管理器
        print("2. 测试代理管理器...")
        stats = proxy_manager.get_proxy_stats()
        print(f"   代理统计: {stats['total_proxies']} 个代理")
        
        # 测试智能反爬虫（简化版）
        print("3. 测试智能反爬虫配置...")
        browser_config = await anti_crawler.create_stealth_browser_config()
        print(f"   浏览器配置: {browser_config.browser_type}")
        
        print("✅ 基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

async def fix_crawl4ai_config():
    """修复crawl4ai配置问题"""
    print("🔧 修复crawl4ai配置...")
    
    try:
        from crawl4ai import CrawlerRunConfig
        
        # 测试正确的配置参数
        config = CrawlerRunConfig(
            wait_until="domcontentloaded",
            page_timeout=30000,
            delay_before_return_html=2.0
        )
        
        print("✅ CrawlerRunConfig配置修复成功")
        return True
        
    except Exception as e:
        print(f"❌ CrawlerRunConfig修复失败: {str(e)}")
        return False

def create_demo_proxy_config():
    """创建演示代理配置"""
    print("🔧 创建演示代理配置...")
    
    demo_config = """
# 演示代理配置
# 请将以下内容添加到config.py的PROXY_CONFIG中

PROXY_CONFIG = {
    "enable_proxy": False,  # 暂时禁用代理进行测试
    "proxy_pool": [
        # 生产环境请使用高质量代理IP
        # "http://username:<EMAIL>:8080",
    ],
    "rotation_strategy": "round_robin",
    "max_requests_per_proxy": 50,
}
"""
    
    with open("demo_proxy_config.txt", "w", encoding="utf-8") as f:
        f.write(demo_config)
    
    print("✅ 演示配置已保存到 demo_proxy_config.txt")

async def test_simple_crawl():
    """测试简单爬取"""
    print("🔧 测试简单爬取功能...")
    
    try:
        from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig
        
        # 使用最简单的配置
        browser_config = BrowserConfig(
            headless=True,
            browser_type="chromium"
        )
        
        crawler_config = CrawlerRunConfig(
            wait_until="domcontentloaded",
            page_timeout=15000
        )
        
        # 测试访问一个简单页面
        test_url = "https://httpbin.org/ip"
        
        async with AsyncWebCrawler(config=browser_config) as crawler:
            result = await crawler.arun(url=test_url, config=crawler_config)
            
            if result.success:
                print(f"✅ 简单爬取测试成功，页面长度: {len(result.html)}")
                return True
            else:
                print(f"❌ 简单爬取测试失败")
                return False
                
    except Exception as e:
        print(f"❌ 简单爬取测试异常: {str(e)}")
        return False

def show_next_steps():
    """显示下一步操作建议"""
    print("\n" + "="*60)
    print("🎯 下一步操作建议:")
    print("="*60)
    
    print("\n1. 📋 配置代理IP（推荐）:")
    print("   - 编辑 config.py 中的 PROXY_CONFIG")
    print("   - 添加高质量代理IP到 proxy_pool")
    print("   - 设置 enable_proxy: True")
    
    print("\n2. 🧪 测试运行:")
    print("   - python main_url_crawler.py --cities=北京")
    print("   - 观察是否能绕过安全检查")
    
    print("\n3. 🔍 监控和调试:")
    print("   - 查看 debug/ 目录下的调试页面")
    print("   - 检查 output/ 目录下的结果文件")
    
    print("\n4. ⚙️ 参数调优:")
    print("   - 调整 ANTI_CRAWLER_CONFIG 中的延时参数")
    print("   - 根据成功率调整并发数和频率")
    
    print("\n5. 📈 性能优化:")
    print("   - 添加更多代理IP")
    print("   - 配置验证码识别服务（可选）")
    print("   - 监控代理IP健康状态")

async def main():
    """主函数"""
    print("🚀 Position_url_crawler 快速修复工具")
    print("="*60)
    
    # 执行修复步骤
    steps = [
        ("基本功能测试", test_basic_functionality),
        ("修复crawl4ai配置", fix_crawl4ai_config),
        ("测试简单爬取", test_simple_crawl),
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        print(f"\n🔧 执行: {step_name}")
        try:
            if asyncio.iscoroutinefunction(step_func):
                result = await step_func()
            else:
                result = step_func()
            
            if result:
                success_count += 1
        except Exception as e:
            print(f"❌ {step_name} 执行失败: {str(e)}")
    
    # 创建演示配置
    create_demo_proxy_config()
    
    # 显示结果
    print(f"\n📊 修复结果: {success_count}/{len(steps)} 项成功")
    
    if success_count == len(steps):
        print("✅ 所有修复项目完成！")
    else:
        print("⚠️ 部分修复项目需要手动处理")
    
    # 显示下一步建议
    show_next_steps()

if __name__ == "__main__":
    asyncio.run(main())
