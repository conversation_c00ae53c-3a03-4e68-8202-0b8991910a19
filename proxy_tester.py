#!/usr/bin/env python3
"""
代理IP测试工具
专门测试和验证代理IP的可用性
"""

import asyncio
import aiohttp
import requests
import time
from typing import List, Dict, Tuple
from config import PROXY_CONFIG

class ProxyTester:
    """代理IP测试器"""
    
    def __init__(self):
        self.test_urls = [
            "https://httpbin.org/ip",
            "https://api.ipify.org?format=json",
            "https://www.zhipin.com",
            "https://www.baidu.com"
        ]
    
    async def test_proxy_async(self, proxy_url: str) -> Dict:
        """异步测试代理"""
        result = {
            "proxy": proxy_url,
            "status": "unknown",
            "response_time": 0,
            "ip_info": None,
            "error": None,
            "test_results": {}
        }
        
        try:
            timeout = aiohttp.ClientTimeout(total=10)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                start_time = time.time()
                
                # 测试IP获取
                async with session.get(
                    "https://httpbin.org/ip",
                    proxy=proxy_url
                ) as response:
                    response_time = time.time() - start_time
                    result["response_time"] = response_time
                    
                    if response.status == 200:
                        ip_data = await response.json()
                        result["ip_info"] = ip_data
                        result["status"] = "success"
                        
                        print(f"✅ 代理测试成功: {proxy_url}")
                        print(f"   响应时间: {response_time:.2f}秒")
                        print(f"   出口IP: {ip_data.get('origin', 'unknown')}")
                    else:
                        result["status"] = "failed"
                        result["error"] = f"HTTP {response.status}"
                        print(f"❌ 代理测试失败: {proxy_url} - HTTP {response.status}")
                        
        except Exception as e:
            result["status"] = "error"
            result["error"] = str(e)
            print(f"❌ 代理测试异常: {proxy_url} - {str(e)}")
        
        return result
    
    def test_proxy_sync(self, proxy_url: str) -> Dict:
        """同步测试代理"""
        result = {
            "proxy": proxy_url,
            "status": "unknown", 
            "response_time": 0,
            "ip_info": None,
            "error": None
        }
        
        try:
            proxies = {
                "http": proxy_url,
                "https": proxy_url
            }
            
            start_time = time.time()
            
            response = requests.get(
                "https://httpbin.org/ip",
                proxies=proxies,
                timeout=10
            )
            
            response_time = time.time() - start_time
            result["response_time"] = response_time
            
            if response.status_code == 200:
                ip_data = response.json()
                result["ip_info"] = ip_data
                result["status"] = "success"
                
                print(f"✅ 同步代理测试成功: {proxy_url}")
                print(f"   响应时间: {response_time:.2f}秒")
                print(f"   出口IP: {ip_data.get('origin', 'unknown')}")
            else:
                result["status"] = "failed"
                result["error"] = f"HTTP {response.status_code}"
                print(f"❌ 同步代理测试失败: {proxy_url} - HTTP {response.status_code}")
                
        except Exception as e:
            result["status"] = "error"
            result["error"] = str(e)
            print(f"❌ 同步代理测试异常: {proxy_url} - {str(e)}")
        
        return result
    
    async def test_boss_access(self, proxy_url: str) -> Dict:
        """测试通过代理访问BOSS直聘"""
        result = {
            "proxy": proxy_url,
            "boss_access": False,
            "page_length": 0,
            "has_security_check": False,
            "error": None
        }
        
        try:
            timeout = aiohttp.ClientTimeout(total=15)
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            }
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(
                    "https://www.zhipin.com/web/geek/jobs?city=101010100&position=100000&page=1",
                    proxy=proxy_url,
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        html = await response.text()
                        result["boss_access"] = True
                        result["page_length"] = len(html)
                        
                        # 检查是否有安全检查
                        security_indicators = [
                            "网站访客身份验证",
                            "请稍候",
                            "安全验证",
                            "滑块验证"
                        ]
                        
                        for indicator in security_indicators:
                            if indicator in html:
                                result["has_security_check"] = True
                                break
                        
                        status = "有安全检查" if result["has_security_check"] else "正常访问"
                        print(f"✅ BOSS访问成功: {proxy_url} - {status}")
                        print(f"   页面长度: {result['page_length']}")
                    else:
                        result["error"] = f"HTTP {response.status}"
                        print(f"❌ BOSS访问失败: {proxy_url} - HTTP {response.status}")
                        
        except Exception as e:
            result["error"] = str(e)
            print(f"❌ BOSS访问异常: {proxy_url} - {str(e)}")
        
        return result
    
    async def comprehensive_test(self) -> Dict:
        """综合测试所有代理"""
        print("🚀 开始综合代理测试")
        print("="*60)
        
        proxy_list = PROXY_CONFIG.get("proxy_pool", [])
        
        if not proxy_list:
            print("❌ 未配置代理IP")
            return {"error": "未配置代理IP"}
        
        print(f"📋 测试代理数量: {len(proxy_list)}")
        print("-"*60)
        
        results = {
            "total_proxies": len(proxy_list),
            "working_proxies": 0,
            "boss_accessible": 0,
            "proxy_results": []
        }
        
        for i, proxy in enumerate(proxy_list, 1):
            print(f"\n🔍 测试代理 {i}/{len(proxy_list)}: {proxy}")
            print("-"*40)
            
            # 基础连接测试
            basic_result = await self.test_proxy_async(proxy)
            
            if basic_result["status"] == "success":
                results["working_proxies"] += 1
                
                # BOSS直聘访问测试
                boss_result = await self.test_boss_access(proxy)
                
                if boss_result["boss_access"]:
                    results["boss_accessible"] += 1
                
                # 合并结果
                basic_result.update(boss_result)
            
            results["proxy_results"].append(basic_result)
            
            # 测试间隔
            await asyncio.sleep(1)
        
        return results
    
    def print_summary(self, results: Dict):
        """打印测试总结"""
        print("\n" + "="*60)
        print("📊 代理测试总结")
        print("="*60)
        
        if "error" in results:
            print(f"❌ 测试失败: {results['error']}")
            return
        
        total = results["total_proxies"]
        working = results["working_proxies"]
        boss_ok = results["boss_accessible"]
        
        print(f"总代理数: {total}")
        print(f"可用代理: {working} ({working/total*100:.1f}%)" if total > 0 else "可用代理: 0")
        print(f"BOSS可访问: {boss_ok} ({boss_ok/total*100:.1f}%)" if total > 0 else "BOSS可访问: 0")
        
        # 显示最佳代理
        best_proxies = [
            r for r in results["proxy_results"] 
            if r["status"] == "success" and r.get("boss_access", False)
        ]
        
        if best_proxies:
            print(f"\n✅ 推荐代理 (共{len(best_proxies)}个):")
            for proxy_result in best_proxies:
                proxy = proxy_result["proxy"]
                response_time = proxy_result["response_time"]
                has_check = proxy_result.get("has_security_check", False)
                check_status = "有安全检查" if has_check else "无安全检查"
                
                print(f"  - {proxy} (响应时间: {response_time:.2f}s, {check_status})")
        else:
            print("\n❌ 没有找到可用于BOSS直聘的代理")
        
        # 显示问题代理
        failed_proxies = [
            r for r in results["proxy_results"]
            if r["status"] != "success"
        ]
        
        if failed_proxies:
            print(f"\n❌ 问题代理 (共{len(failed_proxies)}个):")
            for proxy_result in failed_proxies:
                proxy = proxy_result["proxy"]
                error = proxy_result.get("error", "未知错误")
                print(f"  - {proxy}: {error}")

async def main():
    """主函数"""
    print("🔧 代理IP配置和测试工具")
    print("="*60)
    print(f"📍 本地代理IP: ***************")
    print(f"✅ 已自动配置到 config.py")
    print("="*60)
    
    tester = ProxyTester()
    
    # 执行综合测试
    results = await tester.comprehensive_test()
    
    # 打印总结
    tester.print_summary(results)
    
    # 保存测试结果
    import json
    import os
    
    os.makedirs("debug", exist_ok=True)
    
    with open("debug/proxy_test_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细测试结果已保存到: debug/proxy_test_results.json")
    
    # 给出建议
    if results.get("boss_accessible", 0) > 0:
        print("\n💡 建议:")
        print("  ✅ 有可用代理，可以运行优化后的爬虫")
        print("  🚀 执行: python final_optimized_crawler.py")
    else:
        print("\n💡 建议:")
        print("  ⚠️ 代理IP可能需要调整端口或认证信息")
        print("  🔧 常见端口: 8080, 3128, 1080, 8888, 80, 443")
        print("  🔑 如需认证: http://username:password@***************:port")

if __name__ == "__main__":
    asyncio.run(main())
