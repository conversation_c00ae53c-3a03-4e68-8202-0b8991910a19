#!/usr/bin/env python3
"""
滑块验证码自动解决器
专门处理BOSS直聘的滑块验证机制
"""

import asyncio
import time
import random
from typing import Optional, Tuple
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig

class SliderCaptchaSolver:
    """滑块验证码解决器"""
    
    def __init__(self):
        self.user_agents = [
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
    
    async def solve_slider_captcha(self, url: str) -> Tuple[bool, str, str]:
        """解决滑块验证码"""
        try:
            print(f"🧩 开始解决滑块验证码: {url}")
            
            browser_config = BrowserConfig(
                headless=False,  # 显示浏览器以便调试
                browser_type="chromium",
                viewport_width=1920,
                viewport_height=1080,
                user_agent=random.choice(self.user_agents)
            )
            
            crawler_config = CrawlerRunConfig(
                wait_until="domcontentloaded",
                page_timeout=30000,
                delay_before_return_html=3.0,
                js_code=[
                    # 滑块验证码解决脚本
                    """
                    // 等待滑块元素加载
                    await new Promise(resolve => {
                        const checkSlider = () => {
                            const slider = document.querySelector('.verify-row-code, .slider-btn, .slide-btn');
                            if (slider) {
                                resolve(slider);
                            } else {
                                setTimeout(checkSlider, 100);
                            }
                        };
                        checkSlider();
                    });
                    
                    // 模拟人类滑动行为
                    function simulateHumanSlide() {
                        const slider = document.querySelector('.verify-row-code, .slider-btn, .slide-btn');
                        const track = document.querySelector('.verify-slider-track, .slider-track');
                        
                        if (slider && track) {
                            console.log('找到滑块元素，开始模拟滑动...');
                            
                            // 获取滑动距离
                            const trackWidth = track.offsetWidth;
                            const sliderWidth = slider.offsetWidth;
                            const slideDistance = trackWidth - sliderWidth - 10;
                            
                            // 创建鼠标事件
                            const rect = slider.getBoundingClientRect();
                            const startX = rect.left + rect.width / 2;
                            const startY = rect.top + rect.height / 2;
                            
                            // 鼠标按下
                            slider.dispatchEvent(new MouseEvent('mousedown', {
                                clientX: startX,
                                clientY: startY,
                                bubbles: true
                            }));
                            
                            // 模拟人类滑动轨迹
                            let currentX = startX;
                            const steps = 20;
                            const stepDistance = slideDistance / steps;
                            
                            function slideStep(step) {
                                if (step >= steps) {
                                    // 鼠标释放
                                    document.dispatchEvent(new MouseEvent('mouseup', {
                                        clientX: currentX,
                                        clientY: startY,
                                        bubbles: true
                                    }));
                                    return;
                                }
                                
                                // 添加随机抖动模拟人类行为
                                const jitter = (Math.random() - 0.5) * 2;
                                currentX += stepDistance + jitter;
                                
                                // 鼠标移动
                                document.dispatchEvent(new MouseEvent('mousemove', {
                                    clientX: currentX,
                                    clientY: startY + (Math.random() - 0.5) * 2,
                                    bubbles: true
                                }));
                                
                                // 随机延时模拟人类速度
                                setTimeout(() => slideStep(step + 1), 50 + Math.random() * 50);
                            }
                            
                            slideStep(0);
                        } else {
                            console.log('未找到滑块元素');
                        }
                    }
                    
                    // 执行滑动
                    simulateHumanSlide();
                    
                    // 等待验证完成
                    await new Promise(resolve => {
                        let attempts = 0;
                        const checkSuccess = () => {
                            attempts++;
                            
                            // 检查是否验证成功（页面跳转或内容变化）
                            if (window.location.href !== originalUrl || 
                                document.title !== '网站访客身份验证 - BOSS直聘' ||
                                document.querySelector('.job-list, .job-item')) {
                                console.log('滑块验证成功！');
                                resolve(true);
                            } else if (attempts > 100) {  // 10秒超时
                                console.log('滑块验证超时');
                                resolve(false);
                            } else {
                                setTimeout(checkSuccess, 100);
                            }
                        };
                        
                        const originalUrl = window.location.href;
                        setTimeout(checkSuccess, 1000);  // 1秒后开始检查
                    });
                    """
                ]
            )
            
            async with AsyncWebCrawler(config=browser_config) as crawler:
                result = await crawler.arun(url=url, config=crawler_config)
                
                if result.success and result.html:
                    # 检查是否成功绕过验证
                    if self._is_slider_page(result.html):
                        return False, result.html, "滑块验证未完成"
                    elif self._has_job_content(result.html):
                        return True, result.html, "滑块验证成功，获取到职位页面"
                    else:
                        return True, result.html, "滑块验证完成，但页面内容需要进一步分析"
                else:
                    return False, "", "页面访问失败"
                    
        except Exception as e:
            return False, "", f"滑块验证异常: {str(e)}"
    
    async def solve_with_manual_intervention(self, url: str) -> Tuple[bool, str, str]:
        """手动干预解决滑块验证"""
        try:
            print(f"👤 手动干预模式: {url}")
            print("请在浏览器中手动完成滑块验证...")
            
            browser_config = BrowserConfig(
                headless=False,  # 必须显示浏览器
                browser_type="chromium",
                viewport_width=1920,
                viewport_height=1080,
                user_agent=random.choice(self.user_agents)
            )
            
            crawler_config = CrawlerRunConfig(
                wait_until="domcontentloaded",
                page_timeout=60000,  # 给用户足够时间
                delay_before_return_html=5.0
            )
            
            async with AsyncWebCrawler(config=browser_config) as crawler:
                result = await crawler.arun(url=url, config=crawler_config)
                
                if result.success and result.html:
                    if self._has_job_content(result.html):
                        return True, result.html, "手动验证成功"
                    else:
                        return False, result.html, "手动验证未完成或页面异常"
                else:
                    return False, "", "页面访问失败"
                    
        except Exception as e:
            return False, "", f"手动验证异常: {str(e)}"
    
    def _is_slider_page(self, html_content: str) -> bool:
        """检查是否是滑块验证页面"""
        slider_indicators = [
            "网站访客身份验证",
            "wrap-verify-slider",
            "page-verify-slider",
            "verify-row-code"
        ]
        
        for indicator in slider_indicators:
            if indicator in html_content:
                return True
        
        return False
    
    def _has_job_content(self, html_content: str) -> bool:
        """检查是否包含职位内容"""
        job_indicators = [
            "job-list",
            "job-item", 
            "职位",
            "薪资",
            "工作经验",
            "学历要求"
        ]
        
        job_count = 0
        for indicator in job_indicators:
            if indicator in html_content:
                job_count += 1
        
        return job_count >= 2

async def test_slider_solver():
    """测试滑块解决器"""
    print("🧩 滑块验证码解决器测试")
    print("="*50)
    
    solver = SliderCaptchaSolver()
    test_url = "https://www.zhipin.com/web/geek/jobs?city=101010100&position=100000&page=1"
    
    print(f"测试URL: {test_url}")
    print("-"*50)
    
    # 方法1: 自动解决
    print("\n🤖 方法1: 自动解决滑块验证")
    success1, html1, message1 = await solver.solve_slider_captcha(test_url)
    print(f"结果: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"消息: {message1}")
    print(f"页面长度: {len(html1)}")
    
    if not success1:
        # 方法2: 手动干预
        print("\n👤 方法2: 手动干预模式")
        print("⚠️ 即将打开浏览器，请手动完成滑块验证")
        input("按回车键继续...")
        
        success2, html2, message2 = await solver.solve_with_manual_intervention(test_url)
        print(f"结果: {'✅ 成功' if success2 else '❌ 失败'}")
        print(f"消息: {message2}")
        print(f"页面长度: {len(html2)}")
        
        if success2:
            # 保存成功页面
            with open("debug/manual_success.html", "w", encoding="utf-8") as f:
                f.write(html2)
            print("✅ 成功页面已保存到 debug/manual_success.html")

if __name__ == "__main__":
    asyncio.run(test_slider_solver())
